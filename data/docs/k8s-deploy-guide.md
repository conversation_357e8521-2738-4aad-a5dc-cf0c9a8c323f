# Kubernetes 发布系统使用指南

## 系统概述

Kubernetes 应用发布管理系统是基于 K8s 的现代化应用部署平台，提供可视化的应用发布、管理和监控功能。

## 访问地址

- **系统地址**: https://k8s-app.firstshare.cn/
- **登录方式**: 使用公司统一账号登录

## 主要功能

### 1. 应用管理
- **应用创建**: 支持多种应用类型创建
- **版本管理**: 应用版本发布和回滚
- **配置管理**: 环境变量和配置文件管理
- **资源配置**: CPU、内存、存储资源分配

### 2. 部署管理
- **一键部署**: 简化的部署流程
- **灰度发布**: 支持蓝绿部署和滚动更新
- **环境隔离**: 开发、测试、生产环境分离
- **自动扩缩容**: 基于负载的自动伸缩

### 3. 监控运维
- **实时监控**: 应用运行状态监控
- **日志查看**: 集成日志查看功能
- **健康检查**: 应用健康状态检测
- **告警通知**: 异常情况自动告警

## 快速开始

### 1. 创建应用

1. 登录系统后，点击"创建应用"
2. 填写应用基本信息：
   - 应用名称
   - 应用描述
   - 选择命名空间
   - 设置标签

3. 配置应用参数：
   - 镜像地址
   - 端口配置
   - 环境变量
   - 资源限制

### 2. 部署应用

1. 在应用列表中选择要部署的应用
2. 点击"部署"按钮
3. 选择部署环境（开发/测试/生产）
4. 确认配置信息
5. 点击"确认部署"

### 3. 监控应用

1. 在应用详情页面查看运行状态
2. 查看实时日志
3. 监控资源使用情况
4. 设置告警规则

## 部署策略

### 滚动更新 (Rolling Update)
- **特点**: 逐步替换旧版本实例
- **优势**: 零停机部署
- **适用**: 大部分应用场景

### 蓝绿部署 (Blue-Green)
- **特点**: 同时运行两个版本
- **优势**: 快速回滚
- **适用**: 对稳定性要求高的应用

### 金丝雀发布 (Canary)
- **特点**: 小流量验证新版本
- **优势**: 风险可控
- **适用**: 重要业务系统

## 配置管理

### 环境变量
```yaml
env:
  - name: DATABASE_URL
    value: "mysql://localhost:3306/mydb"
  - name: REDIS_HOST
    value: "redis-service"
```

### 配置文件
```yaml
configMap:
  name: app-config
  data:
    application.yml: |
      server:
        port: 8080
      spring:
        datasource:
          url: ${DATABASE_URL}
```

### 密钥管理
```yaml
secret:
  name: app-secret
  data:
    database-password: <base64-encoded>
    api-key: <base64-encoded>
```

## 资源配置

### CPU 和内存
```yaml
resources:
  requests:
    cpu: "100m"
    memory: "128Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"
```

### 存储配置
```yaml
volumeMounts:
  - name: data-volume
    mountPath: /data
volumes:
  - name: data-volume
    persistentVolumeClaim:
      claimName: app-pvc
```

## 网络配置

### Service 配置
```yaml
service:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
```

### Ingress 配置
```yaml
ingress:
  enabled: true
  hosts:
    - host: myapp.example.com
      paths:
        - path: /
          pathType: Prefix
```

## 监控和日志

### 健康检查
```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /ready
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
```

### 日志收集
- 系统自动收集容器标准输出日志
- 支持日志级别过滤
- 提供日志搜索和下载功能

## 故障排查

### 常见问题

1. **Pod 启动失败**
   - 检查镜像是否存在
   - 验证资源配置是否合理
   - 查看事件日志

2. **服务无法访问**
   - 检查 Service 配置
   - 验证端口映射
   - 确认网络策略

3. **应用频繁重启**
   - 检查健康检查配置
   - 查看应用日志
   - 验证资源限制

### 调试命令
```bash
# 查看 Pod 状态
kubectl get pods -n <namespace>

# 查看 Pod 详情
kubectl describe pod <pod-name> -n <namespace>

# 查看日志
kubectl logs <pod-name> -n <namespace>

# 进入容器
kubectl exec -it <pod-name> -n <namespace> -- /bin/bash
```

## 最佳实践

### 1. 镜像管理
- 使用语义化版本标签
- 避免使用 `latest` 标签
- 定期清理无用镜像

### 2. 资源配置
- 合理设置资源请求和限制
- 根据实际使用情况调整
- 使用 HPA 实现自动扩缩容

### 3. 安全配置
- 使用非 root 用户运行应用
- 启用网络策略
- 定期更新基础镜像

### 4. 监控告警
- 设置关键指标监控
- 配置合理的告警阈值
- 建立故障响应流程

## 联系支持

如果在使用过程中遇到问题，可以：

1. 查看系统内置帮助文档
2. 联系运维团队：<EMAIL>
3. 在技术群中咨询：K8s技术支持群

---

**更新时间**: 2024年12月
**维护团队**: DevOps 团队