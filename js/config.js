/**
 * FaciShare 导航配置文件
 * 在这里可以自定义各种设置
 */

// 时间主题配置
window.NavSphereConfig = {
    // 时间主题设置
    timeTheme: {
        enabled: true,              // 是否启用时间主题自动切换
        lightStart: 6,              // 浅色主题开始时间 (小时, 0-23)
        lightEnd: 18,               // 浅色主题结束时间 (小时, 0-23)
        
        // 预设配置（可以直接使用）
        presets: {
            // 早起模式：5:00-19:00 浅色
            earlyBird: { lightStart: 5, lightEnd: 19 },
            
            // 标准模式：6:00-18:00 浅色 (默认)
            standard: { lightStart: 6, lightEnd: 18 },
            
            // 夜猫子模式：8:00-20:00 浅色
            nightOwl: { lightStart: 8, lightEnd: 20 },
            
            // 办公模式：9:00-17:00 浅色
            office: { lightStart: 9, lightEnd: 17 },
            
            // 极简模式：只在中午使用浅色 12:00-14:00
            minimal: { lightStart: 12, lightEnd: 14 },
            
            // 禁用时间主题，只使用手动切换
            disabled: { enabled: false }
        }
    },
    
    // 搜索配置
    search: {
        maxResults: 10,             // 最大搜索结果数
        debounceTime: 300,          // 搜索防抖时间 (毫秒)
        highlightMatches: true      // 是否高亮匹配关键词
    },
    
    // 界面配置
    ui: {
        showToasts: true,           // 是否显示Toast提示
        animationSpeed: 'normal',   // 动画速度: 'fast', 'normal', 'slow'
        compactMode: false          // 是否使用紧凑模式
    },

    // 时间范围提示配置 (现在通过配置文件管理)
    // 配置文件路径: data/time-notifications.json
    // 通过 data/appconfig.json 中的 timeNotifications 配置引用
    timeRangeNotification: {
        enabled: true,              // 降级配置，当配置文件加载失败时使用
        notifications: []           // 空数组，实际配置从文件加载
    }
};

/**
 * 应用时间主题预设
 * @param {string} presetName 预设名称
 */
window.applyTimeThemePreset = function(presetName) {
    const preset = window.NavSphereConfig.timeTheme.presets[presetName];
    if (!preset) {
        console.error(`预设 "${presetName}" 不存在`);
        console.log('可用预设:', Object.keys(window.NavSphereConfig.timeTheme.presets));
        return;
    }
    
    // 应用预设配置
    if (window.navApp?.themeManager) {
        window.navApp.themeManager.updateTimeThemeConfig(preset);
        
        // 更新全局配置
        Object.assign(window.NavSphereConfig.timeTheme, preset);
        
        console.log(`✅ 已应用时间主题预设: ${presetName}`, preset);
        
        if (typeof showToast === 'function') {
            showToast(`已应用预设: ${presetName}`, 'success', 2000);
        }
    }
};

/**
 * 时间范围提示配置管理
 * 支持从配置文件加载和管理通知
 */
window.TimeNotificationConfig = {
    // 添加新的时间范围提示
    addNotification: (config) => {
        if (!config.id) {
            config.id = 'notification-' + Date.now();
        }

        // 如果应用已初始化，直接添加到管理器
        if (window.navApp?.timeNotificationManager) {
            window.navApp.timeNotificationManager.addNotification(config);
            console.log('✅ 已添加时间范围提示:', config.id);
            return config.id;
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return null;
        }
    },

    // 移除时间范围提示
    removeNotification: (notificationId) => {
        if (window.navApp?.timeNotificationManager) {
            const success = window.navApp.timeNotificationManager.removeNotification(notificationId);
            if (success) {
                console.log('✅ 已移除时间范围提示:', notificationId);
            } else {
                console.warn('未找到指定的时间范围提示:', notificationId);
            }
            return success;
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return false;
        }
    },

    // 启用/禁用时间范围提示功能
    setEnabled: (enabled) => {
        if (window.navApp?.timeNotificationManager) {
            window.navApp.timeNotificationManager.updateConfig({ enabled });
            console.log('✅ 时间范围提示功能已', enabled ? '启用' : '禁用');
        } else {
            console.warn('时间范围提示管理器尚未初始化');
        }
    },

    // 清除所有显示记录
    clearAllRecords: () => {
        if (window.navApp?.timeNotificationManager) {
            window.navApp.timeNotificationManager.clearAllRecords();
            console.log('✅ 已清除所有时间范围提示记录');
        } else {
            console.warn('时间范围提示管理器尚未初始化');
        }
    },

    // 获取当前状态
    getStatus: () => {
        if (window.navApp?.timeNotificationManager) {
            return window.navApp.timeNotificationManager.getStatus();
        }
        return { enabled: false, message: '管理器尚未初始化' };
    },

    // 显示当前配置
    showConfig: () => {
        console.group('📋 时间范围提示配置');

        if (window.navApp?.timeNotificationManager) {
            const manager = window.navApp.timeNotificationManager;
            const status = manager.getStatus();
            const notifications = manager.getAllNotifications();

            console.log('配置来源: 配置文件 (data/time-notifications.json)');
            console.log('启用状态:', status.enabled);
            console.log('配置加载状态:', status.configLoaded);
            console.log('通知数量:', notifications.length);
            console.log('检查间隔:', status.checkInterval + 'ms');

            notifications.forEach((notification, index) => {
                console.log(`通知 ${index + 1}:`, {
                    id: notification.id,
                    name: notification.name || notification.id,
                    type: notification.type,
                    timeRange: notification.timeRange,
                    title: notification.content?.title,
                    enabled: notification.enabled
                });
            });

            console.log('管理器状态:', status);
        } else {
            console.log('时间范围提示管理器尚未初始化');
        }

        console.groupEnd();
    },

    // 重新加载配置文件
    reloadConfig: async () => {
        if (window.navApp?.timeNotificationManager) {
            const success = await window.navApp.timeNotificationManager.reloadConfig();
            if (success) {
                console.log('✅ 配置文件重新加载成功');
            } else {
                console.warn('❌ 配置文件重新加载失败');
            }
            return success;
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return false;
        }
    },

    // 加载预设配置
    loadPreset: async (presetName) => {
        if (window.navApp?.timeNotificationManager) {
            const success = await window.navApp.timeNotificationManager.loadPreset(presetName);
            if (success) {
                console.log(`✅ 已加载预设配置: ${presetName}`);
            } else {
                console.warn(`❌ 加载预设配置失败: ${presetName}`);
            }
            return success;
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return false;
        }
    },

    // 获取可用的预设列表
    getAvailablePresets: async () => {
        if (window.navApp?.timeNotificationManager) {
            const presets = await window.navApp.timeNotificationManager.getAvailablePresets();
            console.log('📋 可用预设配置:');
            presets.forEach(preset => {
                console.log(`• ${preset.id}: ${preset.name} (${preset.notificationCount} 个通知)`);
                console.log(`  ${preset.description}`);
            });
            return presets;
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return [];
        }
    },

    // 获取所有通知
    getAllNotifications: () => {
        if (window.navApp?.timeNotificationManager) {
            return window.navApp.timeNotificationManager.getAllNotifications();
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return [];
        }
    },

    // 获取启用的通知
    getEnabledNotifications: () => {
        if (window.navApp?.timeNotificationManager) {
            return window.navApp.timeNotificationManager.getEnabledNotifications();
        } else {
            console.warn('时间范围提示管理器尚未初始化');
            return [];
        }
    }
};

/**
 * 快速配置函数
 */
window.NavSphereQuickConfig = {
    // 设置时间主题
    setTimeTheme: (lightStart, lightEnd) => {
        if (window.navApp?.themeManager) {
            window.navApp.themeManager.updateTimeThemeConfig({
                enabled: true,
                lightStart,
                lightEnd
            });
            console.log(`✅ 时间主题已设置: ${lightStart}:00-${lightEnd}:00 使用浅色主题`);
        }
    },
    
    // 禁用时间主题
    disableTimeTheme: () => {
        if (window.navApp?.themeManager) {
            window.navApp.themeManager.updateTimeThemeConfig({ enabled: false });
            console.log('✅ 时间主题已禁用');
        }
    },
    
    // 启用时间主题
    enableTimeTheme: () => {
        if (window.navApp?.themeManager) {
            window.navApp.themeManager.updateTimeThemeConfig({ enabled: true });
            console.log('✅ 时间主题已启用');
        }
    },
    
    // 显示当前配置
    showConfig: () => {
        console.group('📋 NavSphere 当前配置');
        
        if (window.navApp?.themeManager) {
            const themeSource = window.navApp.themeManager.getThemeSource();
            const timeConfig = window.navApp.themeManager.getTimeThemeConfig();
            
            console.log('🎨 当前主题:', themeSource.theme, `(${themeSource.description})`);
            console.log('⏰ 时间主题:', timeConfig.enabled ? '启用' : '禁用');
            
            if (timeConfig.enabled) {
                console.log('   浅色时间:', `${timeConfig.lightStart}:00 - ${timeConfig.lightEnd}:00`);
                console.log('   深色时间:', `${timeConfig.lightEnd}:00 - 次日${timeConfig.lightStart}:00`);
            }
        }
        
        console.log('🔧 全局配置:', window.NavSphereConfig);
        console.groupEnd();
    }
};

// 在控制台显示配置提示
console.group('🌐 FaciShare 导航配置');
console.log('时间主题已启用! 当前设置:', window.NavSphereConfig.timeTheme);
console.log('');
console.log('📖 使用方法:');
console.log('• 应用预设: applyTimeThemePreset("nightOwl")');
console.log('• 自定义时间: NavSphereQuickConfig.setTimeTheme(7, 19)');
console.log('• 禁用时间主题: NavSphereQuickConfig.disableTimeTheme()');
console.log('• 查看当前配置: NavSphereQuickConfig.showConfig()');
console.log('');
console.log('⌨️ 快捷键:');
const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
const cmdKey = isMac ? '⌘' : 'Ctrl';
console.log(`• ${cmdKey}+K: 快速搜索`);
console.log('• ESC: 清空搜索/关闭侧边栏');
console.log(`• ${cmdKey}+ESC: 重置主题配置`);
console.log('• ?: 显示帮助信息');
console.log('');
console.log('🗂️ 分类导航使用提示:');
console.log('• 点击分类名称: 查看该分类所有内容');
console.log('• 点击箭头图标: 展开/折叠子分类');
console.log('• 父分类会显示所有子分类的网站');
console.log('');
console.log('⏰ 时间范围提示功能 (配置文件驱动):');
console.log('• 查看配置: TimeNotificationConfig.showConfig()');
console.log('• 重新加载配置: TimeNotificationConfig.reloadConfig()');
console.log('• 查看可用预设: TimeNotificationConfig.getAvailablePresets()');
console.log('• 加载预设: TimeNotificationConfig.loadPreset("student")');
console.log('• 添加提示: TimeNotificationConfig.addNotification({...})');
console.log('• 清除记录: TimeNotificationConfig.clearAllRecords()');
console.log('');
console.log('🎯 可用预设:', Object.keys(window.NavSphereConfig.timeTheme.presets).join(', '));
console.groupEnd();