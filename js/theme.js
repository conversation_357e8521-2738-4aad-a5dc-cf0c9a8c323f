/**
 * 主题管理器
 * 负责处理多主题切换、持久化存储、系统偏好检测和时间自动切换
 */
class ThemeManager {
    constructor() {
        // 可用主题列表
        this.availableThemes = {
            'ivory-light': {
                name: '日光象牙白',
                icon: 'fa-sun',
                description: '温暖舒适的浅色主题'
            },
            'dark-obsidian': {
                name: '夜月玄玉黑', 
                icon: 'fa-moon',
                description: '深邃优雅的深色主题'
            },
            'jasmine-green': {
                name: '清雅茉莉绿',
                icon: 'fa-leaf',
                description: '茉莉花香·禅意美学·护眼悦目'
            },
            'navy-blue': {
                name: '深邃海军蓝',
                icon: 'fa-anchor',
                description: '沉稳专业的蓝色主题'
            },
            'star-moonlight-blue': {
                name: '星月流光蓝',
                icon: 'fa-star',
                description: '浩瀚星空·流光溢彩·月华如水'
            }
        };
        
        // 时间主题配置 - 从全局配置获取
        this.timeThemeConfig = window.NavSphereConfig?.timeTheme ? {
            enabled: window.NavSphereConfig.timeTheme.enabled,
            lightStart: window.NavSphereConfig.timeTheme.lightStart,
            lightEnd: window.NavSphereConfig.timeTheme.lightEnd,
            lightTheme: window.NavSphereConfig.timeTheme.lightTheme || 'ivory-light',
            darkTheme: window.NavSphereConfig.timeTheme.darkTheme || 'dark-obsidian'
        } : {
            enabled: true,                    // 是否启用时间主题
            lightStart: 6,                    // 浅色主题开始时间 (小时, 24小时制)
            lightEnd: 18,                     // 浅色主题结束时间 (小时, 24小时制)
            lightTheme: 'ivory-light',        // 白天使用的主题
            darkTheme: 'dark-obsidian',       // 夜晚使用的主题
            // 注：深色主题时间为 lightEnd 到次日 lightStart
        };
        
        this.currentTheme = this.getInitialTheme();
        this.themeSelector = null;
        this.observers = [];
        
        this.init();
    }
    
    /**
     * 初始化主题管理器
     */
    init() {
        // 立即应用初始主题（避免闪烁）
        this.applyTheme(this.currentTheme);
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupThemeToggle();
                this.updateThemeSelector(this.currentTheme);
                this.watchSystemTheme();
            });
        } else {
            this.setupThemeToggle();
            this.updateThemeSelector(this.currentTheme);
            this.watchSystemTheme();
        }
    }
    
    /**
     * 获取初始主题
     * 优先级：本地存储 > 时间自动切换 > 系统偏好 > 默认(ivory-light)
     */
    getInitialTheme() {
        // 检查本地存储（用户手动设置的主题优先级最高）
        const savedTheme = localStorage.getItem('navsphere-theme');
        if (savedTheme && this.availableThemes[savedTheme]) {
            console.log(`使用保存的主题: ${savedTheme}`);
            return savedTheme;
        }
        
        // 检查时间主题
        if (this.timeThemeConfig.enabled) {
            const timeBasedTheme = this.getTimeBasedTheme();
            if (timeBasedTheme) {
                console.log(`使用时间主题: ${timeBasedTheme}`);
                return timeBasedTheme;
            }
        }
        
        // 检查系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            console.log('使用系统深色偏好');
            return this.timeThemeConfig.darkTheme;
        }
        
        console.log('使用默认浅色主题');
        return this.timeThemeConfig.lightTheme;
    }
    
    /**
     * 根据当前时间获取主题
     * @returns {string|null} 时间主题或null
     */
    getTimeBasedTheme() {
        const now = new Date();
        const currentHour = now.getHours();
        const { lightStart, lightEnd, lightTheme, darkTheme } = this.timeThemeConfig;
        
        // 判断当前时间是否在浅色主题时间段内
        const isLightTime = currentHour >= lightStart && currentHour < lightEnd;
        const selectedTheme = isLightTime ? lightTheme : darkTheme;
        
        console.log(`当前时间: ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}, 浅色时间段: ${lightStart}:00-${lightEnd}:00, 应用主题: ${selectedTheme}`);
        
        return selectedTheme;
    }
    
    /**
     * 设置主题选择器
     */
    setupThemeToggle() {
        console.log('开始设置主题选择器...');
        
        // 查找新的主题选择器或创建一个
        this.themeSelector = document.getElementById('themeSelector');
        console.log('主题选择器元素:', this.themeSelector);
        
        if (!this.themeSelector) {
            console.log('未找到主题选择器，尝试查找旧的切换按钮...');
            // 如果没有找到主题选择器，尝试查找旧的切换按钮并替换
            const oldToggle = document.getElementById('themeToggle');
            console.log('旧切换按钮:', oldToggle);
            if (oldToggle) {
                console.log('找到旧按钮，正在创建新的主题选择器...');
                this.createThemeSelector(oldToggle);
            } else {
                console.error('既没有找到主题选择器，也没有找到旧的切换按钮');
                return;
            }
        }
        
        if (this.themeSelector) {
            console.log('设置主题选择器事件...');
            this.setupThemeSelectorEvents();
            this.updateThemeSelector(this.currentTheme);
            console.log('主题选择器设置完成');
        } else {
            console.error('主题选择器设置失败');
        }
    }
    
    /**
     * 创建主题选择器
     */
    createThemeSelector(replaceElement) {
        const dropdown = document.createElement('div');
        dropdown.className = 'theme-selector-dropdown';
        dropdown.id = 'themeSelector';
        
        dropdown.innerHTML = `
            <button class="action-btn theme-selector-btn" id="themeSelectorBtn" title="选择主题">
                <i class="fas fa-palette"></i>
                <span class="theme-name d-none d-lg-inline">主题</span>
                <i class="fas fa-chevron-down theme-dropdown-arrow"></i>
            </button>
            <div class="theme-dropdown-menu" id="themeDropdownMenu">
                ${Object.entries(this.availableThemes).map(([key, theme]) => `
                    <div class="theme-option" data-theme="${key}">
                        <i class="fas ${theme.icon} theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">${theme.name}</span>
                            <span class="theme-option-desc">${theme.description}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        
        replaceElement.parentNode.replaceChild(dropdown, replaceElement);
        this.themeSelector = dropdown;
    }
    
    /**
     * 设置主题选择器事件
     */
    setupThemeSelectorEvents() {
        console.log('开始绑定主题选择器事件...');
        
        const btn = this.themeSelector.querySelector('#themeSelectorBtn');
        const menu = this.themeSelector.querySelector('#themeDropdownMenu');
        const options = this.themeSelector.querySelectorAll('.theme-option');
        
        console.log('找到的元素:', { btn, menu, optionsCount: options.length });
        
        if (!btn) {
            console.error('未找到主题选择器按钮');
            return;
        }
        
        if (!menu) {
            console.error('未找到主题下拉菜单');
            return;
        }
        
        // 切换下拉菜单
        btn.addEventListener('click', (e) => {
            console.log('主题选择器按钮被点击');
            e.stopPropagation();
            e.preventDefault();
            menu.classList.toggle('show');
            console.log('菜单显示状态:', menu.classList.contains('show'));
        });

        // 移动端触摸事件支持
        btn.addEventListener('touchend', (e) => {
            console.log('主题选择器按钮触摸结束');
            e.stopPropagation();
            e.preventDefault();
            menu.classList.toggle('show');
        }, { passive: false });
        
        // 选择主题
        options.forEach((option, index) => {
            option.addEventListener('click', (e) => {
                console.log(`主题选项 ${index} 被点击`);
                e.stopPropagation();
                e.preventDefault();
                const themeKey = option.dataset.theme;
                console.log('选择的主题:', themeKey);
                this.setTheme(themeKey, true);
                menu.classList.remove('show');
                this.showThemeChangeFeedback(themeKey);
            });

            // 移动端触摸事件支持
            option.addEventListener('touchend', (e) => {
                console.log(`主题选项 ${index} 触摸结束`);
                e.stopPropagation();
                e.preventDefault();
                const themeKey = option.dataset.theme;
                console.log('触摸选择的主题:', themeKey);
                this.setTheme(themeKey, true);
                menu.classList.remove('show');
                this.showThemeChangeFeedback(themeKey);
            }, { passive: false });
        });
        
        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (menu.classList.contains('show') && !this.themeSelector.contains(e.target)) {
                console.log('点击外部，关闭主题菜单');
                menu.classList.remove('show');
            }
        });

        // 移动端触摸外部关闭菜单
        document.addEventListener('touchend', (e) => {
            if (menu.classList.contains('show') && !this.themeSelector.contains(e.target)) {
                console.log('触摸外部，关闭主题菜单');
                menu.classList.remove('show');
            }
        });
        
        console.log('主题选择器事件绑定完成');
    }
    
    /**
     * 切换主题（保持兼容性，随机切换到下一个主题）
     */
    toggleTheme() {
        const themeKeys = Object.keys(this.availableThemes);
        const currentIndex = themeKeys.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeKeys.length;
        const newTheme = themeKeys[nextIndex];
        
        // 手动切换时保存到本地存储
        this.setTheme(newTheme, true);
        this.showThemeChangeFeedback(newTheme);
    }
    
    /**
     * 设置主题
     * @param {string} theme 主题名称
     * @param {boolean} saveToStorage 是否保存到本地存储（默认true）
     */
    setTheme(theme, saveToStorage = true) {
        if (!this.availableThemes[theme]) {
            console.warn(`Invalid theme: ${theme}`);
            return;
        }
        
        const oldTheme = this.currentTheme;
        this.currentTheme = theme;
        
        // 应用主题
        this.applyTheme(theme);
        
        // 保存到本地存储（手动切换时才保存）
        if (saveToStorage) {
            localStorage.setItem('navsphere-theme', theme);
            console.log(`主题已保存到本地存储: ${theme}`);
        }
        
        // 更新选择器
        this.updateThemeSelector(theme);
        
        // 通知观察者
        this.notifyObservers(theme, oldTheme);
        
        console.log(`主题已切换: ${oldTheme} → ${theme} ${saveToStorage ? '(已保存)' : '(未保存)'}`);
    }
    
    /**
     * 应用主题
     * @param {string} theme 主题名称
     */
    applyTheme(theme) {
        // 强制设置data-theme属性，确保覆盖系统偏好
        document.body.setAttribute('data-theme', theme);
        
        // 立即更新CSS变量（确保主题立即生效）
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新meta标签(用于移动端状态栏)
        this.updateMetaThemeColor(theme);
        
        // 添加切换动画类
        document.body.classList.add('theme-transitioning');
        
        // 移除动画类
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);
    }
    
    /**
     * 更新主题选择器
     * @param {string} theme 当前主题
     */
    updateThemeSelector(theme) {
        if (!this.themeSelector) return;
        
        const btn = this.themeSelector.querySelector('#themeSelectorBtn');
        const options = this.themeSelector.querySelectorAll('.theme-option');
        
        if (btn) {
            const icon = btn.querySelector('i');
            const themeName = btn.querySelector('.theme-name');
            
            if (icon && this.availableThemes[theme]) {
                // 更新按钮图标
                icon.className = `fas ${this.availableThemes[theme].icon}`;
            }
            
            if (themeName) {
                // 更新按钮文本
                themeName.textContent = this.availableThemes[theme]?.name || '主题';
            }
            
            // 更新按钮标题
            btn.title = `当前主题: ${this.availableThemes[theme]?.name || theme}`;
        }
        
        // 更新选项状态
        options.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });
    }
    
    /**
     * 更新主题图标
     * @param {string} theme 当前主题
     */
    updateThemeIcon(theme) {
        if (!this.themeToggle) return;
        
        const icon = this.themeToggle.querySelector('i');
        if (icon) {
            // 移除所有主题图标类
            icon.classList.remove('fa-moon', 'fa-sun');
            
            // 添加对应的图标类
            icon.classList.add(theme === 'light' ? 'fa-moon' : 'fa-sun');
            
            // 更新标题
            this.themeToggle.title = theme === 'light' ? '切换到深色模式' : '切换到浅色模式';
        }
    }
    
    /**
     * 更新meta主题色
     * @param {string} theme 主题名称
     */
    updateMetaThemeColor(theme) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        // 根据主题设置状态栏颜色
        const colors = {
            'ivory-light': '#ffffff',
            'dark-obsidian': '#0f0f0f',
            'jasmine-green': '#f0f9f0',
            'navy-blue': '#1e3a5f',
            'star-moonlight-blue': '#0a1428'
        };
        
        metaThemeColor.content = colors[theme] || colors['ivory-light'];
    }
    
    /**
     * 监听系统主题变化
     */
    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // 只有没有用户手动设置时才跟随系统
                const savedTheme = localStorage.getItem('navsphere-theme');
                if (!savedTheme) {
                    const systemTheme = e.matches ? this.timeThemeConfig.darkTheme : this.timeThemeConfig.lightTheme;
                    this.setTheme(systemTheme, false);
                }
            });
        }
    }
    
    /**
     * 显示主题切换反馈
     * @param {string} theme 新主题
     */
    showThemeChangeFeedback(theme) {
        const themeName = this.availableThemes[theme]?.name || theme;
        const message = `已切换到 ${themeName}`;
        
        // 如果有showToast函数就使用，否则使用简单的console输出
        if (typeof showToast === 'function') {
            showToast(message, 'success', 2000);
        } else {
            console.log(message);
        }
    }
    
    /**
     * 添加主题变化观察者
     * @param {Function} callback 回调函数
     */
    addObserver(callback) {
        if (typeof callback === 'function') {
            this.observers.push(callback);
        }
    }
    
    /**
     * 移除主题变化观察者
     * @param {Function} callback 回调函数
     */
    removeObserver(callback) {
        const index = this.observers.indexOf(callback);
        if (index > -1) {
            this.observers.splice(index, 1);
        }
    }
    
    /**
     * 通知所有观察者
     * @param {string} newTheme 新主题
     * @param {string} oldTheme 旧主题
     */
    notifyObservers(newTheme, oldTheme) {
        this.observers.forEach(callback => {
            try {
                callback(newTheme, oldTheme);
            } catch (error) {
                console.error('Theme observer error:', error);
            }
        });
    }
    
    /**
     * 获取当前主题
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * 重置主题(清除本地存储，恢复自动主题)
     */
    resetTheme() {
        localStorage.removeItem('navsphere-theme');
        const autoTheme = this.getInitialTheme();
        this.setTheme(autoTheme, false); // 不保存到本地存储
        
        if (typeof showToast === 'function') {
            showToast('已重置为自动主题', 'success', 2000);
        }
    }
    
    /**
     * 获取时间主题配置
     * @returns {Object} 时间主题配置
     */
    getTimeThemeConfig() {
        return { ...this.timeThemeConfig };
    }
    
    /**
     * 更新时间主题配置
     * @param {Object} config 新的配置
     */
    updateTimeThemeConfig(config) {
        this.timeThemeConfig = { ...this.timeThemeConfig, ...config };
        console.log('时间主题配置已更新:', this.timeThemeConfig);
        
        // 如果启用了时间主题且当前没有手动设置的主题，重新应用时间主题
        const savedTheme = localStorage.getItem('navsphere-theme');
        if (this.timeThemeConfig.enabled && !savedTheme) {
            const timeBasedTheme = this.getTimeBasedTheme();
            if (timeBasedTheme && timeBasedTheme !== this.currentTheme) {
                this.setTheme(timeBasedTheme, false);
            }
        }
    }
    
    /**
     * 获取当前主题来源信息
     * @returns {Object} 主题来源信息
     */
    getThemeSource() {
        const savedTheme = localStorage.getItem('navsphere-theme');
        
        if (savedTheme) {
            return {
                theme: savedTheme,
                source: 'manual',
                description: '用户手动设置'
            };
        }
        
        if (this.timeThemeConfig.enabled) {
            const timeTheme = this.getTimeBasedTheme();
            if (timeTheme) {
                return {
                    theme: timeTheme,
                    source: 'time',
                    description: `时间自动切换 (${this.timeThemeConfig.lightStart}:00-${this.timeThemeConfig.lightEnd}:00为${this.availableThemes[this.timeThemeConfig.lightTheme]?.name})`
                };
            }
        }
        
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return {
                theme: this.timeThemeConfig.darkTheme,
                source: 'system',
                description: '系统深色偏好'
            };
        }
        
        return {
            theme: this.timeThemeConfig.lightTheme,
            source: 'default',
            description: '默认主题'
        };
    }
    
    /**
     * 检查是否为深色主题
     * @returns {boolean} 是否为深色主题
     */
    isDarkTheme() {
        return this.currentTheme === 'dark-obsidian';
    }
    
    /**
     * 检查是否为浅色主题
     * @returns {boolean} 是否为浅色主题
     */
    isLightTheme() {
        return this.currentTheme === 'ivory-light';
    }
    
    /**
     * 获取可用主题列表
     * @returns {Object} 可用主题列表
     */
    getAvailableThemes() {
        return { ...this.availableThemes };
    }
}

// 添加主题切换的CSS动画类
const themeStyles = `
.theme-transitioning {
    transition: background-color 0.3s ease, color 0.3s ease !important;
}

.theme-transitioning * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Toast样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-background);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 10000;
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-warning {
    border-left: 4px solid var(--warning-color);
}

.toast-info {
    border-left: 4px solid var(--primary-color);
}

@media (max-width: 767px) {
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
`;

// 添加样式到页面
if (typeof document !== 'undefined') {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = themeStyles;
    document.head.appendChild(styleSheet);
}

        // 全局主题管理器实例
let themeManager;

// 页面加载完成后初始化
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        if (!window.themeManager) {
            window.themeManager = new ThemeManager();
        }
        
        // 绑定全局主题快捷键
        document.addEventListener('keydown', (e) => {
            // 使用平台检测来处理Cmd/Ctrl + ESC重置主题配置
            if (typeof Platform !== 'undefined') {
                const modifierPressed = Platform.isMac ? e.metaKey : e.ctrlKey;
                if (modifierPressed && e.key === 'Escape') {
                    e.preventDefault();
                    if (window.themeManager) {
                        window.themeManager.resetTheme();
                    }
                }
            } else {
                // 降级处理：同时检查metaKey和ctrlKey
                if ((e.metaKey || e.ctrlKey) && e.key === 'Escape') {
                    e.preventDefault();
                    if (window.themeManager) {
                        window.themeManager.resetTheme();
                    }
                }
            }
        });
    });
}

// 导出主题管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
} 